"""
OSF.io Web Scraper

Este módulo fornece funcionalidades para fazer web scraping do site OSF.io,
especificamente para usar a caixa de busca e obter os resultados.

Nota: O OSF.io requer JavaScript, então este scraper usa uma abordagem híbrida:
1. Tenta primeiro com requests simples
2. Se falhar, usa Selenium com Chrome
"""

import logging
import requests
from typing import List, Dict, Optional
from urllib.parse import quote_plus
from bs4 import BeautifulSoup

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Importações opcionais do Selenium
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    import time
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logger.warning("Selenium não disponível. Apenas busca básica será possível.")


class OSFScraper:
    """Classe para fazer web scraping do site OSF.io"""

    def __init__(self, timeout: int = 10, use_selenium: bool = True):
        """
        Inicializa o scraper OSF.io

        Args:
            timeout: Tempo limite para requisições (segundos)
            use_selenium: Se True, usa Selenium quando requests falhar
        """
        self.timeout = timeout
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def _setup_selenium_driver(self):
        """Configura e retorna o driver do Selenium"""
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium não está disponível")

        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    
    def search(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """
        Realiza uma busca no OSF.io e retorna os resultados

        Args:
            query: Termo de busca
            max_results: Número máximo de resultados a retornar

        Returns:
            Lista de dicionários contendo informações dos resultados
        """
        results = []

        try:
            logger.info(f"Buscando no OSF.io: {query}")

            # Construir URL de busca
            search_url = f"https://osf.io/search/?q={quote_plus(query)}"

            # Fazer requisição
            response = self.session.get(search_url, timeout=self.timeout)
            response.raise_for_status()

            # Parse do HTML
            soup = BeautifulSoup(response.content, 'html.parser')

            # Buscar por diferentes seletores possíveis para resultados
            result_elements = []

            # Tentar diferentes seletores
            selectors = [
                '[data-test-search-result]',
                '.search-result',
                '.project-search-result',
                '.search-result-item',
                'article',
                '.result-item'
            ]

            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    result_elements = elements
                    break

            # Se não encontrou com seletores específicos, buscar por links de projeto
            if not result_elements:
                # Buscar por links que parecem ser de projetos OSF
                links = soup.find_all('a', href=True)
                project_links = [link for link in links if '/project/' in link.get('href', '') or link.get('href', '').startswith('/')]
                result_elements = project_links[:max_results]

            # Extrair informações dos resultados
            for i, element in enumerate(result_elements[:max_results]):
                result = self._extract_result_info(element)
                if result:
                    results.append(result)

            logger.info(f"Encontrados {len(results)} resultados para '{query}'")

        except Exception as e:
            logger.error(f"Erro durante a busca: {str(e)}")
            # Não fazer raise para permitir fallback

        # Se não encontrou resultados e Selenium está disponível, tentar com Selenium
        if not results and self.use_selenium:
            logger.info("Tentando busca com Selenium...")
            results = self._search_with_selenium(query, max_results)

        return results

    def _search_with_selenium(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """
        Busca usando Selenium para sites que requerem JavaScript
        """
        if not SELENIUM_AVAILABLE:
            logger.warning("Selenium não disponível")
            return []

        results = []
        driver = None

        try:
            driver = self._setup_selenium_driver()
            logger.info(f"Usando Selenium para buscar: {query}")

            # Acessar o site
            driver.get("https://osf.io/")

            # Aguardar e localizar a caixa de busca
            wait = WebDriverWait(driver, self.timeout)

            # Localizar a caixa de busca (descoberto no debug)
            search_box = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='Search']"))
            )

            if not search_box:
                logger.error("Não foi possível encontrar a caixa de busca")
                return []

            # Realizar a busca
            search_box.clear()
            search_box.send_keys(query)
            search_box.send_keys(Keys.RETURN)

            # Aguardar resultados carregarem
            time.sleep(5)

            # Obter HTML da página
            soup = BeautifulSoup(driver.page_source, 'html.parser')

            # Buscar resultados (seletor descoberto no debug)
            result_elements = soup.select('[class*="result"]')

            if not result_elements:
                # Fallback para outros seletores
                fallback_selectors = [
                    '[data-test-search-result]',
                    '.search-result',
                    '.project-search-result',
                    'article',
                    '.result-item'
                ]

                for selector in fallback_selectors:
                    elements = soup.select(selector)
                    if elements:
                        result_elements = elements
                        break

            # Extrair informações
            for element in result_elements[:max_results]:
                result = self._extract_result_info(element)
                if result:
                    results.append(result)

            logger.info(f"Selenium encontrou {len(results)} resultados")

        except Exception as e:
            logger.error(f"Erro com Selenium: {str(e)}")

        finally:
            if driver:
                driver.quit()

        return results

    def download_project_files(self, project_url: str, download_dir: str = "osf_downloads") -> List[Dict[str, str]]:
        """
        Faz download dos arquivos de um projeto OSF.io

        Args:
            project_url: URL do projeto OSF (ex: https://osf.io/j4bv6/)
            download_dir: Diretório onde salvar os arquivos

        Returns:
            Lista de dicionários com informações dos arquivos baixados
        """
        if not SELENIUM_AVAILABLE:
            logger.error("Selenium é necessário para download de arquivos")
            return []

        import os
        from urllib.parse import urlparse, unquote

        # Criar diretório de download se não existir
        os.makedirs(download_dir, exist_ok=True)

        downloaded_files = []
        driver = None

        try:
            driver = self._setup_selenium_driver()
            logger.info(f"Acessando projeto para download: {project_url}")

            # Normalizar URL do projeto
            if not project_url.endswith('/'):
                project_url += '/'

            # Acessar a aba Files
            files_url = project_url + "files/osfstorage"
            driver.get(files_url)

            # Aguardar página carregar
            time.sleep(8)

            # Obter HTML da página
            soup = BeautifulSoup(driver.page_source, 'html.parser')

            # Procurar por arquivos usando os seletores descobertos no debug
            file_info = self._extract_file_info(soup)

            logger.info(f"Encontrados {len(file_info)} arquivos para download")

            # Fazer download de cada arquivo
            for i, file_data in enumerate(file_info, 1):
                try:
                    logger.info(f"Baixando arquivo {i}/{len(file_info)}: {file_data['name']}")

                    # Fazer download do arquivo
                    downloaded_file = self._download_file(file_data, download_dir)

                    if downloaded_file:
                        downloaded_files.append(downloaded_file)
                        logger.info(f"✓ Download concluído: {downloaded_file['local_path']}")
                    else:
                        logger.warning(f"✗ Falha no download: {file_data['name']}")

                except Exception as e:
                    logger.error(f"Erro ao baixar arquivo {file_data['name']}: {str(e)}")

        except Exception as e:
            logger.error(f"Erro durante download de arquivos: {str(e)}")

        finally:
            if driver:
                driver.quit()

        return downloaded_files

    def _extract_file_info(self, soup) -> List[Dict[str, str]]:
        """
        Extrai informações dos arquivos da página OSF

        Args:
            soup: BeautifulSoup object da página

        Returns:
            Lista de dicionários com informações dos arquivos
        """
        files = []

        try:
            # Procurar por links de arquivo usando os padrões descobertos
            all_links = soup.find_all('a', href=True)

            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)

                # Verificar se é um link de arquivo OSF
                if '/files/osfstorage/' in href and len(text) > 3:
                    # Extrair informações do arquivo
                    file_info = {
                        'name': text,
                        'url': href if href.startswith('http') else f"https://osf.io{href}",
                        'download_url': self._get_download_url(href),
                        'size': 'Unknown',
                        'date': 'Unknown'
                    }

                    # Tentar extrair metadados adicionais do elemento pai
                    parent = link.parent
                    if parent:
                        # Procurar por informações de tamanho
                        size_elem = parent.find(class_=lambda x: x and 'size' in str(x).lower())
                        if size_elem:
                            file_info['size'] = size_elem.get_text(strip=True)

                        # Procurar por informações de data
                        date_elem = parent.find(class_=lambda x: x and 'date' in str(x).lower())
                        if date_elem:
                            file_info['date'] = date_elem.get_text(strip=True)

                    files.append(file_info)

            # Remover duplicatas baseado na URL
            seen_urls = set()
            unique_files = []
            for file_info in files:
                if file_info['url'] not in seen_urls:
                    seen_urls.add(file_info['url'])
                    unique_files.append(file_info)

            return unique_files

        except Exception as e:
            logger.error(f"Erro ao extrair informações de arquivos: {str(e)}")
            return []

    def _get_download_url(self, file_url: str) -> str:
        """
        Converte URL de arquivo OSF para URL de download

        Args:
            file_url: URL do arquivo no OSF

        Returns:
            URL de download direto
        """
        if file_url.startswith('http'):
            return file_url + '?action=download'
        else:
            return f"https://osf.io{file_url}?action=download"

    def _download_file(self, file_info: Dict[str, str], download_dir: str) -> Optional[Dict[str, str]]:
        """
        Faz download de um arquivo específico

        Args:
            file_info: Dicionário com informações do arquivo
            download_dir: Diretório de destino

        Returns:
            Dicionário com informações do arquivo baixado ou None se falhou
        """
        import os


        try:
            download_url = file_info['download_url']
            file_name = file_info['name']

            # Limpar nome do arquivo
            safe_filename = "".join(c for c in file_name if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
            if not safe_filename:
                safe_filename = "unnamed_file"

            # Se não tem extensão, tentar detectar
            if '.' not in safe_filename:
                safe_filename += '.file'

            local_path = os.path.join(download_dir, safe_filename)

            # Fazer download usando requests
            response = self.session.get(download_url, timeout=self.timeout, stream=True)
            response.raise_for_status()

            # Salvar arquivo
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # Verificar se o arquivo foi salvo
            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                return {
                    'name': file_info['name'],
                    'original_url': file_info['url'],
                    'download_url': download_url,
                    'local_path': local_path,
                    'size': file_info.get('size', 'Unknown'),
                    'date': file_info.get('date', 'Unknown'),
                    'file_size_bytes': os.path.getsize(local_path)
                }
            else:
                return None

        except Exception as e:
            logger.error(f"Erro ao baixar arquivo {file_info['name']}: {str(e)}")
            return None
    
    def _extract_result_info(self, element) -> Optional[Dict[str, str]]:
        """
        Extrai informações de um elemento de resultado do OSF.io

        Args:
            element: Elemento BeautifulSoup do resultado

        Returns:
            Dicionário com informações do resultado ou None se erro
        """
        try:
            result = {}

            # Se o elemento é um link direto
            if element.name == 'a' and element.get('href'):
                href = element['href']
                if href.startswith('/'):
                    href = 'https://osf.io' + href
                result['url'] = href
                result['title'] = element.get_text(strip=True)
                return result

            # Buscar por links dentro do elemento (estrutura do OSF.io)
            links = element.find_all('a', href=True)
            main_link = None

            for link in links:
                href = link.get('href', '')
                # Procurar por links de projeto ou preprint
                if any(pattern in href for pattern in ['/project/', '/preprints/', '/', 'osf.io']):
                    main_link = link
                    break

            if main_link:
                href = main_link['href']
                if href.startswith('/'):
                    href = 'https://osf.io' + href
                result['url'] = href

                # Título do link
                title_text = main_link.get_text(strip=True)
                if title_text:
                    result['title'] = title_text

            # Se não encontrou título no link, buscar em outros elementos
            if not result.get('title'):
                # Buscar em diferentes elementos de título
                title_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', '.title', '[class*="title"]']

                for selector in title_selectors:
                    title_elem = element.select_one(selector)
                    if title_elem:
                        title_text = title_elem.get_text(strip=True)
                        if title_text:
                            result['title'] = title_text
                            break

            # Extrair texto completo do elemento para análise
            full_text = element.get_text(strip=True)

            # Tentar extrair informações do texto
            if full_text:
                # Se não temos título ainda, usar as primeiras palavras
                if not result.get('title'):
                    # Pegar as primeiras palavras como título
                    words = full_text.split()
                    if words:
                        result['title'] = ' '.join(words[:10])  # Primeiras 10 palavras

                # Descrição (texto completo limitado)
                if len(full_text) > 50:  # Só se tiver conteúdo substancial
                    desc_text = full_text
                    if len(desc_text) > 200:
                        desc_text = desc_text[:200] + "..."
                    result['description'] = desc_text

            # Buscar autores em elementos específicos
            author_patterns = ['author', 'contributor', 'creator', 'by']
            for pattern in author_patterns:
                author_elem = element.select_one(f'[class*="{pattern}"]')
                if author_elem:
                    author_text = author_elem.get_text(strip=True)
                    if author_text and len(author_text) < 100:  # Evitar textos muito longos
                        result['authors'] = author_text
                        break

            # Buscar data
            date_elem = element.find('time')
            if date_elem:
                date_text = date_elem.get_text(strip=True)
                if date_text:
                    result['date'] = date_text

            # Tipo de projeto (se disponível)
            type_patterns = ['type', 'category', 'kind']
            for pattern in type_patterns:
                type_elem = element.select_one(f'[class*="{pattern}"]')
                if type_elem:
                    type_text = type_elem.get_text(strip=True)
                    if type_text and len(type_text) < 50:
                        result['type'] = type_text
                        break

            # Só retornar se tiver pelo menos título ou URL
            if result.get('title') or result.get('url'):
                return result
            else:
                return None

        except Exception as e:
            logger.warning(f"Erro ao extrair informações do resultado: {str(e)}")
            return None


def search_osf(query: str, max_results: int = 10, timeout: int = 10, use_selenium: bool = True) -> List[Dict[str, str]]:
    """
    Função conveniente para buscar no OSF.io

    Args:
        query: Termo de busca
        max_results: Número máximo de resultados
        timeout: Tempo limite para requisições
        use_selenium: Se True, usa Selenium quando requests falhar

    Returns:
        Lista de resultados da busca
    """
    scraper = OSFScraper(timeout=timeout, use_selenium=use_selenium)
    return scraper.search(query, max_results)


def download_osf_files(project_url: str, download_dir: str = "osf_downloads", timeout: int = 10) -> List[Dict[str, str]]:
    """
    Função conveniente para fazer download dos arquivos de um projeto OSF.io

    Args:
        project_url: URL do projeto OSF (ex: https://osf.io/j4bv6/)
        download_dir: Diretório onde salvar os arquivos (padrão: "osf_downloads")
        timeout: Tempo limite para requisições

    Returns:
        Lista de dicionários com informações dos arquivos baixados
    """
    scraper = OSFScraper(timeout=timeout, use_selenium=True)
    return scraper.download_project_files(project_url, download_dir)


if __name__ == "__main__":
    # Exemplo de uso - Busca
    query = "machine learning"
    print(f"=== Exemplo 1: Busca ===")
    print(f"Buscando por: {query}")

    try:
        results = search_osf(query, max_results=3)

        print(f"\nEncontrados {len(results)} resultados:")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result.get('title', 'Sem título')}")
            print(f"   URL: {result.get('url', 'N/A')}")
            print(f"   Tipo: {result.get('type', 'N/A')}")

    except Exception as e:
        print(f"Erro durante a busca: {e}")

    # Exemplo de uso - Download
    print(f"\n=== Exemplo 2: Download de Arquivos ===")
    project_url = "https://osf.io/j4bv6/"  # Projeto de exemplo
    print(f"Fazendo download dos arquivos de: {project_url}")

    try:
        downloaded_files = download_osf_files(project_url, download_dir="exemplo_downloads")

        if downloaded_files:
            print(f"\n✓ {len(downloaded_files)} arquivos baixados:")
            for i, file_info in enumerate(downloaded_files, 1):
                print(f"\n{i}. {file_info['name']}")
                print(f"   Salvo em: {file_info['local_path']}")
                print(f"   Tamanho: {file_info.get('size', 'Unknown')}")
                print(f"   Bytes: {file_info['file_size_bytes']}")
        else:
            print("Nenhum arquivo foi baixado.")

    except Exception as e:
        print(f"Erro durante o download: {e}")
