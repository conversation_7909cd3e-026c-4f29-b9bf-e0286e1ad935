"""
Aplicação principal usando NumPy e Gradio.

Esta aplicação demonstra várias funcionalidades do NumPy através de uma interface
web interativa criada com Gradio.
"""

import numpy as np
import gradio as gr
import matplotlib.pyplot as plt
from typing import Tuple, List, Union
import io
import base64
import cmath
from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic
from src.advanced_plotting import (
    create_interactive_quadratic_plot,
    create_3d_surface_plot,
    create_discriminant_analysis,
    create_family_comparison,
    create_roots_histogram,
    create_animated_parabola,
    create_completing_square_visualization,
    create_properties_comparison_chart,
    create_solution_method_visualization
)


def solve_quadratic_equation(a: float, b: float, c: float) -> Tuple[str, str]:
    """
    Resolve uma equação de segundo grau ax² + bx + c = 0.

    Args:
        a: Coeficiente de x²
        b: Coeficiente de x
        c: Termo independente

    Returns:
        Tuple com resultado detalhado e gráfico da função
    """
    # Usar o módulo separado para resolver a equação
    solution = solve_quadratic(a, b, c)
    result_text = format_solution(solution)
    plot_fig = plot_quadratic(a, b, c, solution)

    return result_text, plot_fig


def create_advanced_interactive_plot(a: float, b: float, c: float):
    """Cria gráfico interativo avançado usando Plotly."""
    try:
        solution = solve_quadratic(a, b, c)
        fig = create_interactive_quadratic_plot(a, b, c, solution)
        return fig
    except Exception as e:
        return f"Erro ao criar gráfico: {str(e)}"


def create_3d_analysis(a_min: float, a_max: float, b_min: float, b_max: float, c_fixed: float):
    """Cria análise 3D dos coeficientes."""
    try:
        fig = create_3d_surface_plot((a_min, a_max), (b_min, b_max), c_fixed)
        return fig
    except Exception as e:
        return f"Erro ao criar análise 3D: {str(e)}"


def create_discriminant_plot(a_min: float, a_max: float, b_min: float, b_max: float, c_min: float, c_max: float):
    """Cria visualização do discriminante."""
    try:
        fig = create_discriminant_analysis((a_min, a_max), (b_min, b_max), (c_min, c_max))
        return fig
    except Exception as e:
        return f"Erro ao criar análise do discriminante: {str(e)}"


def create_family_plot(base_a: float, base_b: float, base_c: float,
                      var1_a: float, var1_b: float, var1_c: float,
                      var2_a: float, var2_b: float, var2_c: float,
                      var3_a: float, var3_b: float, var3_c: float):
    """Cria comparação de família de equações."""
    try:
        base_coeffs = (base_a, base_b, base_c)
        variations = [(var1_a, var1_b, var1_c), (var2_a, var2_b, var2_c), (var3_a, var3_b, var3_c)]
        fig = create_family_comparison(base_coeffs, variations)
        return fig
    except Exception as e:
        return f"Erro ao criar comparação de família: {str(e)}"


def create_animation_plot(a_start: float, a_end: float, num_steps: int, b: float, c: float):
    """Cria animação da parábola."""
    try:
        a_values = np.linspace(a_start, a_end, num_steps)
        fig = create_animated_parabola(a_values.tolist(), b, c)
        return fig
    except Exception as e:
        return f"Erro ao criar animação: {str(e)}"


def create_completing_square_plot(a: float, b: float, c: float):
    """Cria visualização de completar quadrados."""
    try:
        fig = create_completing_square_visualization(a, b, c)
        return fig
    except Exception as e:
        return f"Erro ao criar visualização de completar quadrados: {str(e)}"


def create_methods_plot(a: float, b: float, c: float):
    """Cria visualização dos métodos de resolução."""
    try:
        fig = create_solution_method_visualization(a, b, c)
        return fig
    except Exception as e:
        return f"Erro ao criar visualização dos métodos: {str(e)}"


def generate_random_array(size: int, distribution: str) -> Tuple[str, str]:
    """
    Gera um array aleatório com a distribuição especificada.
    
    Args:
        size: Tamanho do array
        distribution: Tipo de distribuição ('normal', 'uniform', 'exponential')
    
    Returns:
        Tuple com estatísticas do array e gráfico
    """
    if distribution == "normal":
        arr = np.random.normal(0, 1, size)
    elif distribution == "uniform":
        arr = np.random.uniform(-1, 1, size)
    elif distribution == "exponential":
        arr = np.random.exponential(1, size)
    else:
        arr = np.random.random(size)
    
    # Calcular estatísticas
    stats = f"""
    📊 Estatísticas do Array:
    • Tamanho: {arr.size}
    • Média: {np.mean(arr):.4f}
    • Desvio Padrão: {np.std(arr):.4f}
    • Mínimo: {np.min(arr):.4f}
    • Máximo: {np.max(arr):.4f}
    • Mediana: {np.median(arr):.4f}
    """
    
    # Criar gráfico
    plt.figure(figsize=(10, 6))
    plt.subplot(1, 2, 1)
    plt.hist(arr, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title(f'Histograma - Distribuição {distribution.title()}')
    plt.xlabel('Valor')
    plt.ylabel('Frequência')
    
    plt.subplot(1, 2, 2)
    plt.plot(arr[:min(100, len(arr))], 'o-', markersize=3, linewidth=1)
    plt.title('Primeiros 100 valores')
    plt.xlabel('Índice')
    plt.ylabel('Valor')
    
    plt.tight_layout()
    
    return stats, plt


def matrix_operations(matrix_a: str, matrix_b: str, operation: str) -> str:
    """
    Realiza operações entre matrizes.
    
    Args:
        matrix_a: Matriz A como string
        matrix_b: Matriz B como string
        operation: Tipo de operação
    
    Returns:
        Resultado da operação
    """
    try:
        # Converter strings para arrays NumPy
        a = np.array(eval(matrix_a))
        b = np.array(eval(matrix_b))
        
        if operation == "Adição":
            result = a + b
            op_symbol = "+"
        elif operation == "Subtração":
            result = a - b
            op_symbol = "-"
        elif operation == "Multiplicação":
            result = a * b
            op_symbol = "*"
        elif operation == "Multiplicação de Matrizes":
            result = np.dot(a, b)
            op_symbol = "@"
        elif operation == "Divisão":
            result = a / b
            op_symbol = "/"
        else:
            return "Operação não suportada"
        
        return f"""
        🔢 Operação: {operation}
        
        Matriz A:
        {a}
        
        {op_symbol}
        
        Matriz B:
        {b}
        
        =
        
        Resultado:
        {result}
        
        📏 Dimensões do resultado: {result.shape}
        """
        
    except Exception as e:
        return f"❌ Erro: {str(e)}\n\nVerifique se as matrizes estão no formato correto: [[1,2],[3,4]]"


def fourier_analysis(signal_type: str, frequency: float, noise_level: float) -> Tuple[str, str]:
    """
    Demonstra análise de Fourier com NumPy.
    
    Args:
        signal_type: Tipo de sinal
        frequency: Frequência do sinal
        noise_level: Nível de ruído
    
    Returns:
        Informações do sinal e gráficos
    """
    # Gerar sinal
    t = np.linspace(0, 1, 1000)
    
    if signal_type == "Senoidal":
        signal = np.sin(2 * np.pi * frequency * t)
    elif signal_type == "Cossenoidal":
        signal = np.cos(2 * np.pi * frequency * t)
    elif signal_type == "Quadrada":
        signal = np.sign(np.sin(2 * np.pi * frequency * t))
    else:
        signal = np.sin(2 * np.pi * frequency * t)
    
    # Adicionar ruído
    noise = np.random.normal(0, noise_level, len(signal))
    noisy_signal = signal + noise
    
    # Análise de Fourier
    fft = np.fft.fft(noisy_signal)
    freqs = np.fft.fftfreq(len(t), t[1] - t[0])
    
    # Informações
    info = f"""
    🌊 Análise de Fourier:
    • Tipo de sinal: {signal_type}
    • Frequência: {frequency} Hz
    • Nível de ruído: {noise_level}
    • Pontos do sinal: {len(signal)}
    • Frequência dominante: {freqs[np.argmax(np.abs(fft[:len(fft)//2]))]} Hz
    """
    
    # Gráficos
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(t[:200], signal[:200], 'b-', label='Sinal original')
    plt.title('Sinal Original')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(t[:200], noisy_signal[:200], 'r-', label='Sinal com ruído')
    plt.title('Sinal com Ruído')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(freqs[:len(freqs)//2], np.abs(fft[:len(fft)//2]))
    plt.title('Espectro de Frequência')
    plt.xlabel('Frequência (Hz)')
    plt.ylabel('Magnitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    plt.specgram(noisy_signal, Fs=1/(t[1]-t[0]))
    plt.title('Espectrograma')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Frequência (Hz)')
    
    plt.tight_layout()
    
    return info, plt


def create_interface():
    """Cria a interface Gradio."""
    
    with gr.Blocks(title="NumPy + Gradio Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🔢 NumPy + Gradio Demo

        Esta aplicação demonstra várias funcionalidades do NumPy através de uma interface interativa.
        Explore as diferentes abas para ver exemplos de:
        - Geração de arrays aleatórios
        - Operações com matrizes
        - Resolução de equações quadráticas
        - Análise de Fourier
        """)
        
        with gr.Tabs():
            # Aba 1: Arrays Aleatórios
            with gr.TabItem("🎲 Arrays Aleatórios"):
                gr.Markdown("### Geração e Análise de Arrays Aleatórios")
                
                with gr.Row():
                    with gr.Column():
                        size_input = gr.Slider(
                            minimum=10, maximum=10000, value=1000, step=10,
                            label="Tamanho do Array"
                        )
                        dist_input = gr.Dropdown(
                            choices=["normal", "uniform", "exponential"],
                            value="normal",
                            label="Distribuição"
                        )
                        generate_btn = gr.Button("Gerar Array", variant="primary")
                    
                    with gr.Column():
                        stats_output = gr.Textbox(
                            label="Estatísticas",
                            lines=8,
                            max_lines=10
                        )
                
                plot_output = gr.Plot(label="Visualização")
                
                generate_btn.click(
                    fn=generate_random_array,
                    inputs=[size_input, dist_input],
                    outputs=[stats_output, plot_output]
                )
            
            # Aba 2: Operações com Matrizes
            with gr.TabItem("🔢 Operações com Matrizes"):
                gr.Markdown("### Operações Matemáticas entre Matrizes")
                gr.Markdown("**Formato das matrizes:** `[[1,2],[3,4]]` para matriz 2x2")
                
                with gr.Row():
                    with gr.Column():
                        matrix_a_input = gr.Textbox(
                            label="Matriz A",
                            value="[[1,2],[3,4]]",
                            placeholder="[[1,2],[3,4]]"
                        )
                        matrix_b_input = gr.Textbox(
                            label="Matriz B",
                            value="[[5,6],[7,8]]",
                            placeholder="[[5,6],[7,8]]"
                        )
                        operation_input = gr.Dropdown(
                            choices=["Adição", "Subtração", "Multiplicação", "Multiplicação de Matrizes", "Divisão"],
                            value="Adição",
                            label="Operação"
                        )
                        calc_btn = gr.Button("Calcular", variant="primary")
                    
                    with gr.Column():
                        result_output = gr.Textbox(
                            label="Resultado",
                            lines=15,
                            max_lines=20
                        )
                
                calc_btn.click(
                    fn=matrix_operations,
                    inputs=[matrix_a_input, matrix_b_input, operation_input],
                    outputs=[result_output]
                )
            
            # Aba 3: Equações Quadráticas
            with gr.TabItem("📐 Equações Quadráticas"):
                gr.Markdown("### Resolução de Equações de Segundo Grau")
                gr.Markdown("**Formato:** ax² + bx + c = 0")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("#### Coeficientes da Equação")
                        a_input = gr.Number(
                            label="Coeficiente a (x²)",
                            value=1,
                            info="Se a = 0, será uma equação linear"
                        )
                        b_input = gr.Number(
                            label="Coeficiente b (x)",
                            value=-5,
                            info="Coeficiente do termo linear"
                        )
                        c_input = gr.Number(
                            label="Coeficiente c (termo independente)",
                            value=6,
                            info="Termo constante da equação"
                        )
                        solve_btn = gr.Button("Resolver Equação", variant="primary")

                        # Exemplos pré-definidos
                        gr.Markdown("#### 📚 Exemplos Rápidos")
                        with gr.Row():
                            example1_btn = gr.Button("x² - 5x + 6 = 0", size="sm")
                            example2_btn = gr.Button("x² - 4x + 4 = 0", size="sm")
                        with gr.Row():
                            example3_btn = gr.Button("x² + x + 1 = 0", size="sm")
                            example4_btn = gr.Button("2x - 4 = 0", size="sm")

                    with gr.Column():
                        equation_result = gr.Textbox(
                            label="Solução da Equação",
                            lines=12,
                            max_lines=15
                        )

                equation_plot = gr.Plot(label="Gráfico da Função")

                # Eventos dos botões
                solve_btn.click(
                    fn=solve_quadratic_equation,
                    inputs=[a_input, b_input, c_input],
                    outputs=[equation_result, equation_plot]
                )

                # Exemplos pré-definidos
                example1_btn.click(
                    lambda: (1, -5, 6),
                    outputs=[a_input, b_input, c_input]
                )
                example2_btn.click(
                    lambda: (1, -4, 4),
                    outputs=[a_input, b_input, c_input]
                )
                example3_btn.click(
                    lambda: (1, 1, 1),
                    outputs=[a_input, b_input, c_input]
                )
                example4_btn.click(
                    lambda: (0, 2, -4),
                    outputs=[a_input, b_input, c_input]
                )

            # Aba 4: Gráficos Avançados
            with gr.TabItem("📊 Gráficos Avançados"):
                gr.Markdown("### Visualizações Avançadas e Interativas")

                with gr.Tabs():
                    # Sub-aba 1: Gráfico Interativo
                    with gr.TabItem("🎯 Gráfico Interativo"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Coeficientes da Equação")
                                adv_a = gr.Number(label="a", value=1)
                                adv_b = gr.Number(label="b", value=-5)
                                adv_c = gr.Number(label="c", value=6)
                                interactive_btn = gr.Button("Criar Gráfico Interativo", variant="primary")

                            with gr.Column():
                                interactive_plot = gr.Plot(label="Gráfico Interativo Plotly")

                        interactive_btn.click(
                            fn=create_advanced_interactive_plot,
                            inputs=[adv_a, adv_b, adv_c],
                            outputs=[interactive_plot]
                        )

                    # Sub-aba 2: Análise 3D
                    with gr.TabItem("🌐 Análise 3D"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Ranges dos Coeficientes")
                                a_min = gr.Number(label="a mínimo", value=-2)
                                a_max = gr.Number(label="a máximo", value=2)
                                b_min = gr.Number(label="b mínimo", value=-5)
                                b_max = gr.Number(label="b máximo", value=5)
                                c_3d = gr.Number(label="c fixo", value=0)
                                analysis_3d_btn = gr.Button("Criar Análise 3D", variant="primary")

                            with gr.Column():
                                plot_3d = gr.Plot(label="Superfície 3D")

                        analysis_3d_btn.click(
                            fn=create_3d_analysis,
                            inputs=[a_min, a_max, b_min, b_max, c_3d],
                            outputs=[plot_3d]
                        )

                    # Sub-aba 3: Discriminante
                    with gr.TabItem("🔍 Análise do Discriminante"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Ranges para Análise")
                                disc_a_min = gr.Number(label="a mínimo", value=-3)
                                disc_a_max = gr.Number(label="a máximo", value=3)
                                disc_b_min = gr.Number(label="b mínimo", value=-5)
                                disc_b_max = gr.Number(label="b máximo", value=5)
                                disc_c_min = gr.Number(label="c mínimo", value=-3)
                                disc_c_max = gr.Number(label="c máximo", value=3)
                                discriminant_btn = gr.Button("Analisar Discriminante", variant="primary")

                            with gr.Column():
                                discriminant_plot = gr.Plot(label="Mapa do Discriminante")

                        discriminant_btn.click(
                            fn=create_discriminant_plot,
                            inputs=[disc_a_min, disc_a_max, disc_b_min, disc_b_max, disc_c_min, disc_c_max],
                            outputs=[discriminant_plot]
                        )

                    # Sub-aba 4: Comparação de Família
                    with gr.TabItem("👥 Família de Equações"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Equação Base")
                                fam_base_a = gr.Number(label="a base", value=1)
                                fam_base_b = gr.Number(label="b base", value=-5)
                                fam_base_c = gr.Number(label="c base", value=6)

                                gr.Markdown("#### Variações")
                                with gr.Row():
                                    with gr.Column():
                                        gr.Markdown("**Variação 1**")
                                        fam_var1_a = gr.Number(label="a1", value=1)
                                        fam_var1_b = gr.Number(label="b1", value=-3)
                                        fam_var1_c = gr.Number(label="c1", value=2)

                                    with gr.Column():
                                        gr.Markdown("**Variação 2**")
                                        fam_var2_a = gr.Number(label="a2", value=2)
                                        fam_var2_b = gr.Number(label="b2", value=-4)
                                        fam_var2_c = gr.Number(label="c2", value=1)

                                    with gr.Column():
                                        gr.Markdown("**Variação 3**")
                                        fam_var3_a = gr.Number(label="a3", value=-1)
                                        fam_var3_b = gr.Number(label="b3", value=2)
                                        fam_var3_c = gr.Number(label="c3", value=3)

                                family_btn = gr.Button("Comparar Família", variant="primary")

                            with gr.Column():
                                family_plot = gr.Plot(label="Comparação de Família")

                        family_btn.click(
                            fn=create_family_plot,
                            inputs=[fam_base_a, fam_base_b, fam_base_c,
                                   fam_var1_a, fam_var1_b, fam_var1_c,
                                   fam_var2_a, fam_var2_b, fam_var2_c,
                                   fam_var3_a, fam_var3_b, fam_var3_c],
                            outputs=[family_plot]
                        )

                    # Sub-aba 5: Animação
                    with gr.TabItem("🎬 Animação"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Parâmetros da Animação")
                                anim_a_start = gr.Number(label="a inicial", value=-2)
                                anim_a_end = gr.Number(label="a final", value=2)
                                anim_steps = gr.Slider(label="Número de passos", minimum=5, maximum=50, value=20, step=1)
                                anim_b = gr.Number(label="b fixo", value=0)
                                anim_c = gr.Number(label="c fixo", value=0)
                                animation_btn = gr.Button("Criar Animação", variant="primary")

                            with gr.Column():
                                animation_plot = gr.Plot(label="Animação da Parábola")

                        animation_btn.click(
                            fn=create_animation_plot,
                            inputs=[anim_a_start, anim_a_end, anim_steps, anim_b, anim_c],
                            outputs=[animation_plot]
                        )

                    # Sub-aba 6: Completar Quadrados
                    with gr.TabItem("🧮 Completar Quadrados"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Equação para Análise")
                                comp_a = gr.Number(label="a", value=1)
                                comp_b = gr.Number(label="b", value=-6)
                                comp_c = gr.Number(label="c", value=8)
                                completing_btn = gr.Button("Visualizar Processo", variant="primary")

                            with gr.Column():
                                completing_plot = gr.Plot(label="Processo de Completar Quadrados")

                        completing_btn.click(
                            fn=create_completing_square_plot,
                            inputs=[comp_a, comp_b, comp_c],
                            outputs=[completing_plot]
                        )

                    # Sub-aba 7: Métodos de Resolução
                    with gr.TabItem("🔧 Métodos de Resolução"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Equação para Análise")
                                meth_a = gr.Number(label="a", value=1)
                                meth_b = gr.Number(label="b", value=-5)
                                meth_c = gr.Number(label="c", value=6)
                                methods_btn = gr.Button("Analisar Métodos", variant="primary")

                            with gr.Column():
                                methods_plot = gr.Plot(label="Métodos de Resolução")

                        methods_btn.click(
                            fn=create_methods_plot,
                            inputs=[meth_a, meth_b, meth_c],
                            outputs=[methods_plot]
                        )

            # Aba 5: Análise de Fourier
            with gr.TabItem("🌊 Análise de Fourier"):
                gr.Markdown("### Transformada de Fourier e Análise de Sinais")
                
                with gr.Row():
                    with gr.Column():
                        signal_type_input = gr.Dropdown(
                            choices=["Senoidal", "Cossenoidal", "Quadrada"],
                            value="Senoidal",
                            label="Tipo de Sinal"
                        )
                        frequency_input = gr.Slider(
                            minimum=1, maximum=50, value=5, step=1,
                            label="Frequência (Hz)"
                        )
                        noise_input = gr.Slider(
                            minimum=0, maximum=1, value=0.1, step=0.01,
                            label="Nível de Ruído"
                        )
                        fourier_btn = gr.Button("Analisar", variant="primary")
                    
                    with gr.Column():
                        fourier_info = gr.Textbox(
                            label="Informações do Sinal",
                            lines=8,
                            max_lines=10
                        )
                
                fourier_plot = gr.Plot(label="Análise de Fourier")
                
                fourier_btn.click(
                    fn=fourier_analysis,
                    inputs=[signal_type_input, frequency_input, noise_input],
                    outputs=[fourier_info, fourier_plot]
                )
        
        gr.Markdown("""
        ---
        💡 **Dicas:**
        - Experimente diferentes parâmetros para ver como afetam os resultados
        - Use matrizes pequenas para operações mais rápidas
        - Para equações quadráticas, teste os exemplos pré-definidos
        - A análise de Fourier é útil para processamento de sinais
        - **Novo!** Explore os gráficos avançados para análises visuais profundas
        - Use gráficos interativos para explorar propriedades das equações
        - Animações ajudam a entender como os coeficientes afetam a parábola
        - Análise 3D mostra relações entre múltiplos parâmetros
        """)
    
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando aplicação NumPy + Gradio...")
    
    # Criar e lançar a interface
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )


if __name__ == "__main__":
    main()
