# OSF.io Web Scraper

Este módulo fornece funcionalidades para fazer web scraping do site OSF.io (Open Science Framework), permitindo buscar e extrair informações de projetos de pesquisa.

## ⚠️ Importante

O OSF.io requer JavaScript para funcionar completamente. Este scraper usa uma abordagem híbrida:

1. **Primeira tentativa**: Usa `requests` simples (mais r<PERSON>, mas pode não funcionar)
2. **Fallback**: Usa `Selenium` com Chrome (mais lento, mas mais confiável)

## Funcionalidades

- Busca automática no site OSF.io usando a caixa de busca
- Extração de informações dos resultados (título, URL, autores, descrição, etc.)
- Abordagem híbrida: requests + Selenium como fallback
- Suporte a modo headless (sem interface gráfica)
- Configuração flexível de timeout e número de resultados

## Dependências

As seguintes bibliotecas foram instaladas:

```bash
uv add selenium beautifulsoup4 webdriver-manager requests
```

- `requests`: Para requisições HTTP básicas
- `selenium`: Para automação do navegador web
- `beautifulsoup4`: Para parsing do HTML
- `webdriver-manager`: Para gerenciamento automático do ChromeDriver

## Uso Básico

### Função Simples (Recomendado)

```python
from src.osf_scraper import search_osf

# Busca simples com Selenium (mais confiável)
results = search_osf("machine learning", max_results=5, use_selenium=True)

for result in results:
    print(f"Título: {result.get('title')}")
    print(f"URL: {result.get('url')}")
    print(f"Autores: {result.get('authors')}")
    print("-" * 40)
```

### Classe OSFScraper

```python
from src.osf_scraper import OSFScraper

# Criar instância do scraper
scraper = OSFScraper(timeout=15, use_selenium=True)

# Realizar busca
results = scraper.search("data science", max_results=10)

# Processar resultados
for result in results:
    print(result)
```

### Demo Interativo

Execute o demo para testar interativamente:

```bash
python demo_osf_scraper.py
```

## Parâmetros

### search_osf()

- `query` (str): Termo de busca
- `max_results` (int): Número máximo de resultados (padrão: 10)
- `timeout` (int): Tempo limite para requisições (padrão: 10 segundos)
- `use_selenium` (bool): Se True, usa Selenium quando requests falhar (padrão: True)

### OSFScraper()

- `timeout` (int): Tempo limite para requisições (segundos)
- `use_selenium` (bool): Se True, usa Selenium como fallback quando requests falhar

## Estrutura dos Resultados

Cada resultado é um dicionário que pode conter:

```python
{
    'title': 'Título do projeto',
    'url': 'https://osf.io/xxxxx/',
    'description': 'Descrição do projeto...',
    'authors': 'Nome dos autores',
    'date': 'Data de publicação',
    'type': 'Tipo do projeto'
}
```

## Exemplos de Uso

### 1. Demo interativo (Recomendado)

```bash
python demo_osf_scraper.py
```

### 2. Teste simples

```bash
python test_simple_osf.py
```

### 3. Exemplo programático

```bash
python examples/osf_search_example.py
```

### 4. Uso no código

```python
# Importar a função
from src.osf_scraper import search_osf

# Buscar por um termo específico (com Selenium)
results = search_osf("psychology research", max_results=3, use_selenium=True)

# Exibir resultados
for i, result in enumerate(results, 1):
    print(f"{i}. {result.get('title', 'Sem título')}")
    print(f"   URL: {result.get('url', 'N/A')}")
    print(f"   Autores: {result.get('authors', 'N/A')}")
    print()
```

### 5. Busca apenas com requests (mais rápida, pode não funcionar)

```python
# Busca sem Selenium (mais rápida, mas pode falhar)
results = search_osf("machine learning", max_results=5, use_selenium=False)
```

## Requisitos do Sistema

- Python 3.9+
- Google Chrome instalado
- Conexão com a internet

## Notas Importantes

1. **ChromeDriver**: O `webdriver-manager` instala automaticamente o ChromeDriver compatível
2. **Modo Headless**: Por padrão, o navegador executa sem interface gráfica para melhor performance
3. **Rate Limiting**: Evite fazer muitas requisições em sequência para não sobrecarregar o servidor
4. **JavaScript**: O OSF.io requer JavaScript, por isso usamos Selenium em vez de requests simples

## Tratamento de Erros

O scraper inclui tratamento de erros para:

- Timeout de elementos
- Problemas de conexão
- Elementos não encontrados
- Erros de parsing

## Limitações

- Depende da estrutura atual do site OSF.io
- Requer Chrome instalado no sistema
- Pode ser afetado por mudanças no layout do site
- Performance limitada pela velocidade de carregamento das páginas

## Troubleshooting

### Chrome não encontrado
```bash
# Ubuntu/Debian
sudo apt-get install google-chrome-stable

# CentOS/RHEL
sudo yum install google-chrome-stable
```

### Problemas de permissão
```bash
# Dar permissão de execução
chmod +x /path/to/chromedriver
```

### Timeout errors
- Aumentar o valor do parâmetro `timeout`
- Verificar conexão com a internet
- Tentar com `headless=False` para debug visual
