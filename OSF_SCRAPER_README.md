# OSF.io Web Scraper

Este módulo fornece funcionalidades para fazer web scraping do site OSF.io (Open Science Framework), permitindo buscar e extrair informações de projetos de pesquisa.

## ⚠️ Importante

O OSF.io requer JavaScript para funcionar completamente. Este scraper usa uma abordagem híbrida:

1. **Primeira tentativa**: Usa `requests` simples (mais r<PERSON>, mas pode não funcionar)
2. **Fallback**: Usa `Selenium` com Chrome (mais lento, mas mais confiável)

## Funcionalidades

- Busca automática no site OSF.io usando a caixa de busca
- Extração de informações dos resultados (título, URL, autores, descrição, etc.)
- Abordagem híbrida: requests + Selenium como fallback
- Suporte a modo headless (sem interface gráfica)
- Configuração flexível de timeout e número de resultados

## Dependências

As seguintes bibliotecas foram instaladas:

```bash
uv add selenium beautifulsoup4 webdriver-manager requests
```

- `requests`: Para requisições HTTP básicas
- `selenium`: Para automação do navegador web
- `beautifulsoup4`: Para parsing do HTML
- `webdriver-manager`: Para gerenciamento automático do ChromeDriver

## Uso Básico

### Função Simples (Recomendado)

```python
from src.osf_scraper import search_osf

# Busca simples com Selenium (mais confiável)
results = search_osf("machine learning", max_results=5, use_selenium=True)

for result in results:
    print(f"Título: {result.get('title')}")
    print(f"URL: {result.get('url')}")
    print(f"Autores: {result.get('authors')}")
    print("-" * 40)
```

### Classe OSFScraper

```python
from src.osf_scraper import OSFScraper

# Criar instância do scraper
scraper = OSFScraper(timeout=15, use_selenium=True)

# Realizar busca
results = scraper.search("data science", max_results=10)

# Processar resultados
for result in results:
    print(result)
```

### Demo Interativo

Execute o demo para testar interativamente:

```bash
python demo_osf_scraper.py
```

## Parâmetros

### search_osf()

- `query` (str): Termo de busca
- `max_results` (int): Número máximo de resultados (padrão: 10)
- `timeout` (int): Tempo limite para requisições (padrão: 10 segundos)
- `use_selenium` (bool): Se True, usa Selenium quando requests falhar (padrão: True)

### OSFScraper()

- `timeout` (int): Tempo limite para requisições (segundos)
- `use_selenium` (bool): Se True, usa Selenium como fallback quando requests falhar

## Estrutura dos Resultados

Cada resultado é um dicionário que pode conter:

```python
{
    'title': 'Título do projeto',
    'url': 'https://osf.io/xxxxx/',
    'description': 'Descrição do projeto...',
    'authors': 'Nome dos autores',
    'date': 'Data de publicação',
    'type': 'Tipo do projeto'
}
```

## Exemplos de Uso

### 1. Teste simples (Recomendado para começar)

```bash
python test_osf_simple.py
```

### 2. Exemplo prático completo

```bash
python exemplo_uso_osf.py
```

### 3. Demo interativo

```bash
python demo_osf_scraper.py
```

### 4. Uso básico no código

```python
# Importar a função
from src.osf_scraper import search_osf

# Buscar por um termo específico
results = search_osf("python", max_results=3)

# Exibir resultados
for i, result in enumerate(results, 1):
    print(f"{i}. {result.get('title', 'Sem título')}")
    print(f"   URL: {result.get('url', 'N/A')}")
    if result.get('type'):
        print(f"   Tipo: {result['type']}")
    print()
```

### 5. Exemplo com múltiplas buscas

```python
termos = ["machine learning", "psychology", "data science"]

for termo in termos:
    print(f"Buscando: {termo}")
    results = search_osf(termo, max_results=2)

    for result in results:
        print(f"- {result.get('title', 'Sem título')}")
    print()
```

### 6. Busca apenas com requests (mais rápida, mas pode falhar)

```python
# Busca sem Selenium (mais rápida, mas pode não funcionar)
results = search_osf("machine learning", max_results=5, use_selenium=False)
```

## Requisitos do Sistema

- Python 3.9+
- Google Chrome instalado
- Conexão com a internet

## Notas Importantes

1. **ChromeDriver**: O `webdriver-manager` instala automaticamente o ChromeDriver compatível
2. **Modo Headless**: Por padrão, o navegador executa sem interface gráfica para melhor performance
3. **Rate Limiting**: Evite fazer muitas requisições em sequência para não sobrecarregar o servidor
4. **JavaScript**: O OSF.io requer JavaScript, por isso usamos Selenium em vez de requests simples

## Tratamento de Erros

O scraper inclui tratamento de erros para:

- Timeout de elementos
- Problemas de conexão
- Elementos não encontrados
- Erros de parsing

## Limitações

- Depende da estrutura atual do site OSF.io
- Requer Chrome instalado no sistema
- Pode ser afetado por mudanças no layout do site
- Performance limitada pela velocidade de carregamento das páginas

## Troubleshooting

### Chrome não encontrado
```bash
# Ubuntu/Debian
sudo apt-get install google-chrome-stable

# CentOS/RHEL
sudo yum install google-chrome-stable
```

### Problemas de permissão
```bash
# Dar permissão de execução
chmod +x /path/to/chromedriver
```

### Timeout errors
- Aumentar o valor do parâmetro `timeout`
- Verificar conexão com a internet
- Tentar com `headless=False` para debug visual
